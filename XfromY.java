import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringTokenizer;

public class XfromY {

    static class Pair implements Comparable<Pair> {
        int count;
        long cost;

        Pair(int count, long cost) {
            this.count = count;
            this.cost = cost;
        }

        @Override
        public int compareTo(Pair other) {
            if (this.count != other.count) {
                return Integer.compare(this.count, other.count);
            }
            return Long.compare(this.cost, other.cost);
        }
    }

    static class SuffixAutomaton {
        static class State {
            int len, link;
            int[] next = new int[26];
        }

        List<State> st;
        int last;

        SuffixAutomaton(int maxLength) {
            st = new ArrayList<>(2 * maxLength + 2);
            st.add(new State());
            st.get(0).len = 0;
            st.get(0).link = -1;
            last = 0;
        }

        void extend(char c) {
            int charIndex = c - 'a';
            int cur = st.size();
            st.add(new State());
            st.get(cur).len = st.get(last).len + 1;
            int p = last;
            while (p != -1 && st.get(p).next[charIndex] == 0) {
                st.get(p).next[charIndex] = cur;
                p = st.get(p).link;
            }

            if (p == -1) {
                st.get(cur).link = 0;
            } else {
                int q = st.get(p).next[charIndex];
                if (st.get(q).len == st.get(p).len + 1) {
                    st.get(cur).link = q;
                } else {
                    int clone = st.size();
                    st.add(new State());
                    st.get(clone).len = st.get(p).len + 1;
                    st.get(clone).next = Arrays.copyOf(st.get(q).next, 26);
                    st.get(clone).link = st.get(q).link;
                    while (p != -1 && st.get(p).next[charIndex] == q) {
                        st.get(p).next[charIndex] = clone;
                        p = st.get(p).link;
                    }
                    st.get(q).link = clone;
                    st.get(cur).link = clone;
                }
            }
            last = cur;
        }
    }

    static class SegmentTree {
        Pair[] tree;
        int size;
        static final Pair INF = new Pair(Integer.MAX_VALUE, Long.MAX_VALUE);

        SegmentTree(int n) {
            this.size = n;
            tree = new Pair[4 * size];
            Arrays.fill(tree, INF);
        }

        private Pair merge(Pair p1, Pair p2) {
            return p1.compareTo(p2) < 0 ? p1 : p2;
        }

        void update(int node, int start, int end, int idx, Pair val) {
            if (start == end) {
                tree[node] = val;
                return;
            }
            int mid = start + (end - start) / 2;
            if (start <= idx && idx <= mid) {
                update(2 * node + 1, start, mid, idx, val);
            } else {
                update(2 * node + 2, mid + 1, end, idx, val);
            }
            tree[node] = merge(tree[2 * node + 1], tree[2 * node + 2]);
        }

        Pair query(int node, int start, int end, int l, int r) {
            if (r < start || end < l || l > r) {
                return INF;
            }
            if (l <= start && end <= r) {
                return tree[node];
            }
            int mid = start + (end - start) / 2;
            Pair p1 = query(2 * node + 1, start, mid, l, r);
            Pair p2 = query(2 * node + 2, mid + 1, end, l, r);
            return merge(p1, p2);
        }
    }

    static void calculateMatches(String text, SuffixAutomaton sam, int[] matches) {
        int v = 0;
        int l = 0;
        for (int i = 0; i < text.length(); i++) {
            int charIndex = text.charAt(i) - 'a';
            while (v != 0 && sam.st.get(v).next[charIndex] == 0) {
                v = sam.st.get(v).link;
                l = sam.st.get(v).len;
            }
            if (sam.st.get(v).next[charIndex] != 0) {
                v = sam.st.get(v).next[charIndex];
                l++;
            }
            matches[i + 1] = l;
        }
    }

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        String X = br.readLine();
        String Y = br.readLine();
        StringTokenizer st = new StringTokenizer(br.readLine());
        int S = Integer.parseInt(st.nextToken());
        int R = Integer.parseInt(st.nextToken());

        String revY = new StringBuilder(Y).reverse().toString();

        SuffixAutomaton samY = new SuffixAutomaton(Y.length());
        for (char c : Y.toCharArray()) samY.extend(c);

        SuffixAutomaton samRevY = new SuffixAutomaton(revY.length());
        for (char c : revY.toCharArray()) samRevY.extend(c);

        int n = X.length();
        int[] matchY = new int[n + 1];
        int[] matchRevY = new int[n + 1];
        calculateMatches(X, samY, matchY);
        calculateMatches(X, samRevY, matchRevY);

        Pair[] dp = new Pair[n + 1];
        for (int i = 0; i <= n; i++) {
            dp[i] = new Pair(Integer.MAX_VALUE, Long.MAX_VALUE);
        }
        dp[0] = new Pair(0, 0);

        SegmentTree segS = new SegmentTree(n + 1);
        SegmentTree segR = new SegmentTree(n + 1);

        segS.update(0, 0, n, 0, new Pair(dp[0].count + 1, dp[0].cost + S));
        segR.update(0, 0, n, 0, new Pair(dp[0].count + 1, dp[0].cost + R));

        for (int i = 1; i <= n; i++) {
            Pair candidateS = SegmentTree.INF;
            int lenS = matchY[i];
            if (lenS > 0) {
                candidateS = segS.query(0, 0, n, i - lenS, i - 1);
            }

            Pair candidateR = SegmentTree.INF;
            int lenR = matchRevY[i];
            if (lenR > 0) {
                candidateR = segR.query(0, 0, n, i - lenR, i - 1);
            }

            dp[i] = candidateS.compareTo(candidateR) < 0 ? candidateS : candidateR;

            if (dp[i].count != Integer.MAX_VALUE) {
                segS.update(0, 0, n, i, new Pair(dp[i].count + 1, dp[i].cost + S));
                segR.update(0, 0, n, i, new Pair(dp[i].count + 1, dp[i].cost + R));
            }
        }

        if (dp[n].count == Integer.MAX_VALUE) {
            System.out.println("Impossible");
        } else {
            System.out.println(dp[n].cost);
        }
    }
}
