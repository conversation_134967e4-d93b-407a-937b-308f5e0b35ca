import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.IOException;
import java.util.StringTokenizer;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

public class TestXfromY {
    
    static class SuffixAutomaton {
        static class State {
            int len, link;
            int[] next = new int[26];
        }

        List<State> st;
        int last;

        SuffixAutomaton(int maxLength) {
            st = new ArrayList<>(2 * maxLength + 2);
            st.add(new State());
            st.get(0).len = 0;
            st.get(0).link = -1;
            last = 0;
        }

        void extend(char c) {
            int charIndex = c - 'a';
            int cur = st.size();
            st.add(new State());
            st.get(cur).len = st.get(last).len + 1;
            int p = last;
            while (p != -1 && st.get(p).next[charIndex] == 0) {
                st.get(p).next[charIndex] = cur;
                p = st.get(p).link;
            }

            if (p == -1) {
                st.get(cur).link = 0;
            } else {
                int q = st.get(p).next[charIndex];
                if (st.get(q).len == st.get(p).len + 1) {
                    st.get(cur).link = q;
                } else {
                    int clone = st.size();
                    st.add(new State());
                    st.get(clone).len = st.get(p).len + 1;
                    st.get(clone).next = Arrays.copyOf(st.get(q).next, 26);
                    st.get(clone).link = st.get(q).link;
                    while (p != -1 && st.get(p).next[charIndex] == q) {
                        st.get(p).next[charIndex] = clone;
                        p = st.get(p).link;
                    }
                    st.get(q).link = clone;
                    st.get(cur).link = clone;
                }
            }
            last = cur;
        }
    }

    static void calculateMatches(String text, SuffixAutomaton sam, int[] matches) {
        int v = 0;
        int l = 0;
        for (int i = 0; i < text.length(); i++) {
            int charIndex = text.charAt(i) - 'a';
            while (v != 0 && sam.st.get(v).next[charIndex] == 0) {
                v = sam.st.get(v).link;
                l = sam.st.get(v).len;
            }
            if (sam.st.get(v).next[charIndex] != 0) {
                v = sam.st.get(v).next[charIndex];
                l++;
            }
            matches[i + 1] = l;
        }
    }

    public static void main(String[] args) {
        String X = "abcdef";
        String Y = "pafedexybc";
        
        String revY = new StringBuilder(Y).reverse().toString();
        System.out.println("X: " + X);
        System.out.println("Y: " + Y);
        System.out.println("revY: " + revY);
        
        SuffixAutomaton samY = new SuffixAutomaton(Y.length());
        for (char c : Y.toCharArray()) samY.extend(c);

        SuffixAutomaton samRevY = new SuffixAutomaton(revY.length());
        for (char c : revY.toCharArray()) samRevY.extend(c);

        int n = X.length();
        int[] matchY = new int[n + 1];
        int[] matchRevY = new int[n + 1];
        calculateMatches(X, samY, matchY);
        calculateMatches(X, samRevY, matchRevY);
        
        System.out.println("matchY: " + Arrays.toString(matchY));
        System.out.println("matchRevY: " + Arrays.toString(matchRevY));
    }
}
