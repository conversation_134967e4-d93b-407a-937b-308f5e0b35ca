
import java.util.LinkedList;
import java.util.Queue;
import java.util.Scanner;
import java.util.Arrays;

public class Game {

    static class Point {
        int x, y;

        Point(int x, int y) {
            this.x = x;
            this.y = y;
        }
    }

    private static boolean isValid(int r, int c, int M, int N, int[][] grid, int[][] dist) {
        return r >= 0 && r < M && c >= 0 && c < N && grid[r][c] == 0 && dist[r][c] == -1;
    }

    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);

        int M = sc.nextInt();
        int N = sc.nextInt();

        int[][] grid = new int[M][N];
        for (int i = 0; i < M; i++) {
            for (int j = 0; j < N; j++) {
                grid[i][j] = sc.nextInt();
            }
        }

        Point source = new Point(sc.nextInt(), sc.nextInt());
        Point destination = new Point(sc.nextInt(), sc.nextInt());
        Point moveRule = new Point(sc.nextInt(), sc.nextInt());

        sc.close();
        
        int[][] dist = new int[M][N];
        for (int i = 0; i < M; i++) {
            Arrays.fill(dist[i], -1);
        }

        Queue<Point> queue = new LinkedList<>();

        if (grid[source.x][source.y] == 1) {
            System.out.println(-1);
            return;
        }
        
        dist[source.x][source.y] = 0;
        queue.add(source);

        int[] dr = {moveRule.x,  moveRule.y, -moveRule.y, -moveRule.x};
        int[] dc = {moveRule.y, -moveRule.x,  moveRule.x, -moveRule.y};
        
        int minMoves = -1;

        while (!queue.isEmpty()) {
            Point current = queue.poll();

            if (current.x == destination.x && current.y == destination.y) {
                minMoves = dist[current.x][current.y];
                break;
            }

            for (int i = 0; i < 4; i++) {
                int nextR = current.x + dr[i];
                int nextC = current.y + dc[i];

                if (isValid(nextR, nextC, M, N, grid, dist)) {
                    dist[nextR][nextC] = dist[current.x][current.y] + 1;
                    queue.add(new Point(nextR, nextC));
                }
            }
        }
        
        System.out.println(minMoves);
    }
}